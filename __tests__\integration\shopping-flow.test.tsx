import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import App from '../../App';
import { AuthProvider } from '../../context/AuthContext';

// Mock external dependencies
vi.mock('../../utils/performance', () => ({
  performanceMonitor: {
    startTiming: vi.fn(),
    endTiming: vi.fn()
  },
  useRenderTime: vi.fn()
}));

vi.mock('../../utils/cache', () => ({
  cache: {
    get: vi.fn(),
    set: vi.fn(),
    has: vi.fn()
  },
  CACHE_KEYS: {},
  CACHE_TTL: {}
}));

// Mock lazy components to avoid dynamic imports in tests
vi.mock('../../components/LazyComponents', () => ({
  LazyProductDetail: ({ product, onBack, onCartAdd }: any) => (
    <div data-testid="product-detail">
      <h1>{product.name}</h1>
      <button onClick={onBack}>Back</button>
      <button onClick={onCartAdd}>Add to Cart</button>
    </div>
  ),
  LazyCheckout: ({ onBackToList }: any) => (
    <div data-testid="checkout">
      <h1>Checkout</h1>
      <button onClick={onBackToList}>Back to List</button>
    </div>
  ),
  LazyPersonalShopper: ({ isOpen, onClose }: any) => 
    isOpen ? (
      <div data-testid="personal-shopper">
        <h1>Personal Shopper</h1>
        <button onClick={onClose}>Close</button>
      </div>
    ) : null,
  LazyProductComparison: ({ isOpen, onClose }: any) =>
    isOpen ? (
      <div data-testid="product-comparison">
        <h1>Product Comparison</h1>
        <button onClick={onClose}>Close</button>
      </div>
    ) : null,
  LazyOrderTracking: ({ isOpen, onClose }: any) =>
    isOpen ? (
      <div data-testid="order-tracking">
        <h1>Order Tracking</h1>
        <button onClick={onClose}>Close</button>
      </div>
    ) : null,
  LazyLoyaltyProgram: ({ isOpen, onClose }: any) =>
    isOpen ? (
      <div data-testid="loyalty-program">
        <h1>Loyalty Program</h1>
        <button onClick={onClose}>Close</button>
      </div>
    ) : null,
  LazyStyleRecommendations: ({ isOpen, onClose }: any) =>
    isOpen ? (
      <div data-testid="style-recommendations">
        <h1>Style Recommendations</h1>
        <button onClick={onClose}>Close</button>
      </div>
    ) : null,
  LazyAdminDashboard: ({ onBack }: any) => (
    <div data-testid="admin-dashboard">
      <h1>Admin Dashboard</h1>
      <button onClick={onBack}>Back</button>
    </div>
  ),
  ComponentLoader: () => <div data-testid="loading">Loading...</div>,
  LazyErrorBoundary: ({ children }: any) => children
}));

describe('Shopping Flow Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('completes full shopping flow: browse -> select -> add to cart -> checkout', async () => {
    render(<App />);

    // 1. Browse products - should see product list
    expect(screen.getByText(/Marcat/)).toBeInTheDocument();
    
    // Wait for products to load
    await waitFor(() => {
      expect(screen.getByText(/Classic White Shirt/)).toBeInTheDocument();
    });

    // 2. Select a product
    const productCard = screen.getByText('Classic White Shirt').closest('div');
    fireEvent.click(productCard!);

    // Should navigate to product detail
    await waitFor(() => {
      expect(screen.getByTestId('product-detail')).toBeInTheDocument();
    });

    // 3. Add to cart
    const addToCartButton = screen.getByText('Add to Cart');
    fireEvent.click(addToCartButton);

    // Cart should open
    await waitFor(() => {
      expect(screen.getByText(/Shopping Cart/)).toBeInTheDocument();
    });

    // 4. Proceed to checkout
    const checkoutButton = screen.getByText('Proceed to Checkout');
    fireEvent.click(checkoutButton);

    // Should navigate to checkout
    await waitFor(() => {
      expect(screen.getByTestId('checkout')).toBeInTheDocument();
    });
  });

  it('handles product comparison flow', async () => {
    render(<App />);

    // Wait for products to load
    await waitFor(() => {
      expect(screen.getByText(/Classic White Shirt/)).toBeInTheDocument();
    });

    // Add products to comparison
    const compareButtons = screen.getAllByLabelText(/Add to comparison/);
    fireEvent.click(compareButtons[0]);
    fireEvent.click(compareButtons[1]);

    // Comparison FAB should appear
    await waitFor(() => {
      expect(screen.getByLabelText(/Open Product Comparison/)).toBeInTheDocument();
    });

    // Open comparison
    const comparisonFAB = screen.getByLabelText(/Open Product Comparison/);
    fireEvent.click(comparisonFAB);

    // Comparison modal should open
    await waitFor(() => {
      expect(screen.getByTestId('product-comparison')).toBeInTheDocument();
    });
  });

  it('handles wishlist functionality', async () => {
    render(<App />);

    // Wait for products to load
    await waitFor(() => {
      expect(screen.getByText(/Classic White Shirt/)).toBeInTheDocument();
    });

    // Add to wishlist
    const wishlistButtons = screen.getAllByLabelText(/Add to wishlist/);
    fireEvent.click(wishlistButtons[0]);

    // Should show success feedback
    await waitFor(() => {
      expect(screen.getByLabelText(/Remove from wishlist/)).toBeInTheDocument();
    });

    // Open wishlist
    const wishlistButton = screen.getByLabelText(/Open wishlist/);
    fireEvent.click(wishlistButton);

    // Wishlist should open
    await waitFor(() => {
      expect(screen.getByText(/Wishlist/)).toBeInTheDocument();
    });
  });

  it('handles search and filtering', async () => {
    render(<App />);

    // Wait for products to load
    await waitFor(() => {
      expect(screen.getByText(/Classic White Shirt/)).toBeInTheDocument();
    });

    // Search for products
    const searchInput = screen.getByPlaceholderText(/Search products/);
    fireEvent.change(searchInput, { target: { value: 'shirt' } });

    // Should show search results
    await waitFor(() => {
      expect(screen.getByText(/Found \d+ results for "shirt"/)).toBeInTheDocument();
    });

    // Filter by category
    const categoryButton = screen.getByText('Shirts');
    fireEvent.click(categoryButton);

    // Should filter products
    await waitFor(() => {
      expect(screen.getByText(/Shirts/)).toBeInTheDocument();
    });
  });

  it('handles personal shopper interaction', async () => {
    render(<App />);

    // Open personal shopper
    const shopperFAB = screen.getByLabelText(/Open personal shopper/);
    fireEvent.click(shopperFAB);

    // Personal shopper should open
    await waitFor(() => {
      expect(screen.getByTestId('personal-shopper')).toBeInTheDocument();
    });

    // Close personal shopper
    const closeButton = screen.getByText('Close');
    fireEvent.click(closeButton);

    // Should close
    await waitFor(() => {
      expect(screen.queryByTestId('personal-shopper')).not.toBeInTheDocument();
    });
  });

  it('handles authentication flow', async () => {
    render(<App />);

    // Click login button
    const loginButton = screen.getByText('Login');
    fireEvent.click(loginButton);

    // Auth modal should open
    await waitFor(() => {
      expect(screen.getByText(/Sign In/)).toBeInTheDocument();
    });

    // Fill login form
    const emailInput = screen.getByLabelText(/Email/);
    const passwordInput = screen.getByLabelText(/Password/);
    
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: 'password123' } });

    // Submit form
    const submitButton = screen.getByText('Sign In');
    fireEvent.click(submitButton);

    // Should show success or error message
    await waitFor(() => {
      expect(screen.getByText(/Welcome/)).toBeInTheDocument();
    });
  });

  it('handles admin dashboard access', async () => {
    // Mock admin user
    const mockAdminUser = {
      id: 'admin-1',
      name: 'Admin User',
      email: '<EMAIL>',
      role: 'admin' as const
    };

    render(
      <AuthProvider value={{ currentUser: mockAdminUser, login: vi.fn(), logout: vi.fn(), register: vi.fn() }}>
        <App />
      </AuthProvider>
    );

    // Navigate to admin dashboard
    const adminButton = screen.getByText('Admin');
    fireEvent.click(adminButton);

    // Admin dashboard should load
    await waitFor(() => {
      expect(screen.getByTestId('admin-dashboard')).toBeInTheDocument();
    });
  });

  it('handles error states gracefully', async () => {
    // Mock network error
    vi.spyOn(console, 'error').mockImplementation(() => {});

    render(<App />);

    // Should still render without crashing
    expect(screen.getByText(/Marcat/)).toBeInTheDocument();
  });

  it('handles responsive design breakpoints', async () => {
    // Mock different viewport sizes
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 768
    });

    render(<App />);

    // Should render mobile-friendly layout
    await waitFor(() => {
      const productGrid = screen.getByRole('main').querySelector('.grid');
      expect(productGrid).toHaveClass('grid-cols-1', 'sm:grid-cols-2');
    });
  });

  it('handles keyboard navigation', async () => {
    render(<App />);

    // Wait for products to load
    await waitFor(() => {
      expect(screen.getByText(/Classic White Shirt/)).toBeInTheDocument();
    });

    // Navigate with keyboard
    const firstProduct = screen.getByText('Classic White Shirt').closest('div');
    firstProduct?.focus();
    
    fireEvent.keyDown(firstProduct!, { key: 'Enter' });

    // Should navigate to product detail
    await waitFor(() => {
      expect(screen.getByTestId('product-detail')).toBeInTheDocument();
    });
  });
});
