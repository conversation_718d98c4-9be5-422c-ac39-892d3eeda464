import '@testing-library/jest-dom';
import { vi } from 'vitest';

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation((callback) => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
  root: null,
  rootMargin: '',
  thresholds: []
}));

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn()
}));

// Mock PerformanceObserver
global.PerformanceObserver = vi.fn().mockImplementation((callback) => ({
  observe: vi.fn(),
  disconnect: vi.fn(),
  takeRecords: vi.fn(() => [])
}));

// Mock performance methods
Object.defineProperty(global.performance, 'now', {
  writable: true,
  value: vi.fn(() => Date.now())
});

Object.defineProperty(global.performance, 'mark', {
  writable: true,
  value: vi.fn()
});

Object.defineProperty(global.performance, 'measure', {
  writable: true,
  value: vi.fn()
});

Object.defineProperty(global.performance, 'getEntriesByType', {
  writable: true,
  value: vi.fn(() => [])
});

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  length: 0,
  key: vi.fn()
};

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

Object.defineProperty(window, 'sessionStorage', {
  value: localStorageMock
});

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn()
  }))
});

// Mock window.scrollTo
Object.defineProperty(window, 'scrollTo', {
  value: vi.fn()
});

// Mock HTMLElement.scrollIntoView
Object.defineProperty(HTMLElement.prototype, 'scrollIntoView', {
  value: vi.fn()
});

// Mock Image constructor
global.Image = class {
  onload: (() => void) | null = null;
  onerror: (() => void) | null = null;
  src: string = '';
  
  constructor() {
    setTimeout(() => {
      if (this.onload) {
        this.onload();
      }
    }, 100);
  }
} as any;

// Mock URL methods
Object.defineProperty(URL, 'createObjectURL', {
  value: vi.fn(() => 'mocked-url')
});

Object.defineProperty(URL, 'revokeObjectURL', {
  value: vi.fn()
});

// Mock fetch
global.fetch = vi.fn();

// Mock console methods
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

beforeEach(() => {
  console.error = vi.fn();
  console.warn = vi.fn();
});

afterEach(() => {
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
  vi.clearAllMocks();
});

// Test utilities
export const mockProduct = {
  id: 'test-product-1',
  name: 'Test Product',
  price: 29.99,
  originalPrice: 39.99,
  image: '/test-image.jpg',
  category: 'Test Category',
  brand: 'Test Brand',
  rating: 4.5,
  reviewCount: 10,
  description: 'A test product description',
  sizes: ['S', 'M', 'L'],
  colors: ['Blue', 'Red'],
  tags: ['test', 'product'],
  storeId: 'test-store-1'
};

export const mockUser = {
  id: 'test-user-1',
  name: 'Test User',
  email: '<EMAIL>',
  role: 'customer' as const
};

export const mockCartItem = {
  id: 'test-cart-item-1',
  productId: 'test-product-1',
  name: 'Test Product',
  price: 29.99,
  image: '/test-image.jpg',
  size: 'M',
  color: 'Blue',
  quantity: 1
};
