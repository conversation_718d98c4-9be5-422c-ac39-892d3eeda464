import { renderHook, act } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { CartProvider, useCart } from '../../context/CartContext';
import type { CartItem } from '../../types';

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
};

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage
});

const wrapper = ({ children }: { children: React.ReactNode }) => (
  <CartProvider>{children}</CartProvider>
);

describe('CartContext', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue(null);
  });

  it('initializes with empty cart', () => {
    const { result } = renderHook(() => useCart(), { wrapper });

    expect(result.current.items).toEqual([]);
    expect(result.current.getItemCount()).toBe(0);
    expect(result.current.getTotal()).toBe(0);
  });

  it('loads cart from localStorage on initialization', () => {
    const savedCart = [
      {
        id: 'cart-1',
        productId: 'prod-1',
        name: 'Test Item',
        price: 29.99,
        image: '/test.jpg',
        size: 'M',
        color: 'Blue',
        quantity: 2
      }
    ];

    mockLocalStorage.getItem.mockReturnValue(JSON.stringify(savedCart));

    const { result } = renderHook(() => useCart(), { wrapper });

    expect(result.current.items).toEqual(savedCart);
    expect(result.current.getItemCount()).toBe(2);
  });

  it('adds new item to cart', () => {
    const { result } = renderHook(() => useCart(), { wrapper });

    const newItem: Omit<CartItem, 'id'> = {
      productId: 'prod-1',
      name: 'Test Item',
      price: 29.99,
      image: '/test.jpg',
      size: 'M',
      color: 'Blue',
      quantity: 1
    };

    act(() => {
      result.current.addItem(newItem);
    });

    expect(result.current.items).toHaveLength(1);
    expect(result.current.items[0]).toMatchObject(newItem);
    expect(result.current.items[0].id).toBeDefined();
  });

  it('increases quantity when adding existing item', () => {
    const { result } = renderHook(() => useCart(), { wrapper });

    const item: Omit<CartItem, 'id'> = {
      productId: 'prod-1',
      name: 'Test Item',
      price: 29.99,
      image: '/test.jpg',
      size: 'M',
      color: 'Blue',
      quantity: 1
    };

    act(() => {
      result.current.addItem(item);
      result.current.addItem(item);
    });

    expect(result.current.items).toHaveLength(1);
    expect(result.current.items[0].quantity).toBe(2);
  });

  it('treats different sizes/colors as separate items', () => {
    const { result } = renderHook(() => useCart(), { wrapper });

    const item1: Omit<CartItem, 'id'> = {
      productId: 'prod-1',
      name: 'Test Item',
      price: 29.99,
      image: '/test.jpg',
      size: 'M',
      color: 'Blue',
      quantity: 1
    };

    const item2: Omit<CartItem, 'id'> = {
      productId: 'prod-1',
      name: 'Test Item',
      price: 29.99,
      image: '/test.jpg',
      size: 'L',
      color: 'Blue',
      quantity: 1
    };

    act(() => {
      result.current.addItem(item1);
      result.current.addItem(item2);
    });

    expect(result.current.items).toHaveLength(2);
  });

  it('removes item from cart', () => {
    const { result } = renderHook(() => useCart(), { wrapper });

    const item: Omit<CartItem, 'id'> = {
      productId: 'prod-1',
      name: 'Test Item',
      price: 29.99,
      image: '/test.jpg',
      size: 'M',
      color: 'Blue',
      quantity: 1
    };

    act(() => {
      result.current.addItem(item);
    });

    const itemId = result.current.items[0].id;

    act(() => {
      result.current.removeItem(itemId);
    });

    expect(result.current.items).toHaveLength(0);
  });

  it('updates item quantity', () => {
    const { result } = renderHook(() => useCart(), { wrapper });

    const item: Omit<CartItem, 'id'> = {
      productId: 'prod-1',
      name: 'Test Item',
      price: 29.99,
      image: '/test.jpg',
      size: 'M',
      color: 'Blue',
      quantity: 1
    };

    act(() => {
      result.current.addItem(item);
    });

    const itemId = result.current.items[0].id;

    act(() => {
      result.current.updateQuantity(itemId, 5);
    });

    expect(result.current.items[0].quantity).toBe(5);
  });

  it('removes item when quantity is set to 0', () => {
    const { result } = renderHook(() => useCart(), { wrapper });

    const item: Omit<CartItem, 'id'> = {
      productId: 'prod-1',
      name: 'Test Item',
      price: 29.99,
      image: '/test.jpg',
      size: 'M',
      color: 'Blue',
      quantity: 1
    };

    act(() => {
      result.current.addItem(item);
    });

    const itemId = result.current.items[0].id;

    act(() => {
      result.current.updateQuantity(itemId, 0);
    });

    expect(result.current.items).toHaveLength(0);
  });

  it('clears entire cart', () => {
    const { result } = renderHook(() => useCart(), { wrapper });

    const item1: Omit<CartItem, 'id'> = {
      productId: 'prod-1',
      name: 'Test Item 1',
      price: 29.99,
      image: '/test1.jpg',
      size: 'M',
      color: 'Blue',
      quantity: 1
    };

    const item2: Omit<CartItem, 'id'> = {
      productId: 'prod-2',
      name: 'Test Item 2',
      price: 39.99,
      image: '/test2.jpg',
      size: 'L',
      color: 'Red',
      quantity: 2
    };

    act(() => {
      result.current.addItem(item1);
      result.current.addItem(item2);
    });

    expect(result.current.items).toHaveLength(2);

    act(() => {
      result.current.clearCart();
    });

    expect(result.current.items).toHaveLength(0);
  });

  it('calculates total item count correctly', () => {
    const { result } = renderHook(() => useCart(), { wrapper });

    const item1: Omit<CartItem, 'id'> = {
      productId: 'prod-1',
      name: 'Test Item 1',
      price: 29.99,
      image: '/test1.jpg',
      size: 'M',
      color: 'Blue',
      quantity: 2
    };

    const item2: Omit<CartItem, 'id'> = {
      productId: 'prod-2',
      name: 'Test Item 2',
      price: 39.99,
      image: '/test2.jpg',
      size: 'L',
      color: 'Red',
      quantity: 3
    };

    act(() => {
      result.current.addItem(item1);
      result.current.addItem(item2);
    });

    expect(result.current.getItemCount()).toBe(5);
  });

  it('calculates total price correctly', () => {
    const { result } = renderHook(() => useCart(), { wrapper });

    const item1: Omit<CartItem, 'id'> = {
      productId: 'prod-1',
      name: 'Test Item 1',
      price: 29.99,
      image: '/test1.jpg',
      size: 'M',
      color: 'Blue',
      quantity: 2
    };

    const item2: Omit<CartItem, 'id'> = {
      productId: 'prod-2',
      name: 'Test Item 2',
      price: 39.99,
      image: '/test2.jpg',
      size: 'L',
      color: 'Red',
      quantity: 1
    };

    act(() => {
      result.current.addItem(item1);
      result.current.addItem(item2);
    });

    // (29.99 * 2) + (39.99 * 1) = 99.97
    expect(result.current.getTotal()).toBe(99.97);
  });

  it('saves cart to localStorage on changes', () => {
    const { result } = renderHook(() => useCart(), { wrapper });

    const item: Omit<CartItem, 'id'> = {
      productId: 'prod-1',
      name: 'Test Item',
      price: 29.99,
      image: '/test.jpg',
      size: 'M',
      color: 'Blue',
      quantity: 1
    };

    act(() => {
      result.current.addItem(item);
    });

    expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
      'marcat_cart',
      expect.stringContaining('Test Item')
    );
  });

  it('handles localStorage errors gracefully', () => {
    mockLocalStorage.setItem.mockImplementation(() => {
      throw new Error('Storage quota exceeded');
    });

    const { result } = renderHook(() => useCart(), { wrapper });

    const item: Omit<CartItem, 'id'> = {
      productId: 'prod-1',
      name: 'Test Item',
      price: 29.99,
      image: '/test.jpg',
      size: 'M',
      color: 'Blue',
      quantity: 1
    };

    // Should not throw error
    expect(() => {
      act(() => {
        result.current.addItem(item);
      });
    }).not.toThrow();
  });
});
