import { describe, it, expect, beforeEach, vi } from 'vitest';
import { cache, persistentCache, CACHE_KEYS, CACHE_TTL } from '../../utils/cache';

describe('Cache Utilities', () => {
  beforeEach(() => {
    cache.clear();
    vi.clearAllMocks();
  });

  describe('In-Memory Cache', () => {
    it('should store and retrieve values', () => {
      const key = 'test-key';
      const value = { data: 'test-data' };

      cache.set(key, value);
      const retrieved = cache.get(key);

      expect(retrieved).toEqual(value);
    });

    it('should return undefined for non-existent keys', () => {
      const result = cache.get('non-existent-key');
      expect(result).toBeUndefined();
    });

    it('should check if key exists', () => {
      const key = 'test-key';
      const value = 'test-value';

      expect(cache.has(key)).toBe(false);
      
      cache.set(key, value);
      expect(cache.has(key)).toBe(true);
    });

    it('should delete keys', () => {
      const key = 'test-key';
      const value = 'test-value';

      cache.set(key, value);
      expect(cache.has(key)).toBe(true);

      cache.delete(key);
      expect(cache.has(key)).toBe(false);
    });

    it('should clear all entries', () => {
      cache.set('key1', 'value1');
      cache.set('key2', 'value2');

      expect(cache.size()).toBe(2);

      cache.clear();
      expect(cache.size()).toBe(0);
    });

    it('should respect TTL expiration', async () => {
      const key = 'test-key';
      const value = 'test-value';
      const ttl = 100; // 100ms

      cache.set(key, value, ttl);
      expect(cache.get(key)).toBe(value);

      // Wait for TTL to expire
      await new Promise(resolve => setTimeout(resolve, 150));
      
      expect(cache.get(key)).toBeUndefined();
    });

    it('should provide cache statistics', () => {
      cache.set('key1', 'value1');
      cache.set('key2', 'value2');

      const stats = cache.getStats();
      
      expect(stats.size).toBe(2);
      expect(stats.hits).toBe(0);
      expect(stats.misses).toBe(0);
    });

    it('should track hits and misses', () => {
      const key = 'test-key';
      const value = 'test-value';

      // Miss
      cache.get(key);
      expect(cache.getStats().misses).toBe(1);

      // Set and hit
      cache.set(key, value);
      cache.get(key);
      expect(cache.getStats().hits).toBe(1);
    });
  });

  describe('Persistent Cache', () => {
    it('should store and retrieve from localStorage', () => {
      const key = 'test-persistent-key';
      const value = { data: 'persistent-data' };

      persistentCache.set(key, value);
      const retrieved = persistentCache.get(key);

      expect(retrieved).toEqual(value);
      expect(localStorage.setItem).toHaveBeenCalled();
    });

    it('should handle localStorage errors gracefully', () => {
      const key = 'test-key';
      const value = 'test-value';

      // Mock localStorage to throw error
      vi.mocked(localStorage.setItem).mockImplementation(() => {
        throw new Error('Storage quota exceeded');
      });

      // Should not throw
      expect(() => persistentCache.set(key, value)).not.toThrow();
    });

    it('should return null for invalid JSON', () => {
      const key = 'invalid-json-key';
      
      vi.mocked(localStorage.getItem).mockReturnValue('invalid-json');
      
      const result = persistentCache.get(key);
      expect(result).toBeNull();
    });
  });

  describe('Cache Constants', () => {
    it('should have defined cache keys', () => {
      expect(CACHE_KEYS).toBeDefined();
      expect(typeof CACHE_KEYS).toBe('object');
    });

    it('should have defined TTL values', () => {
      expect(CACHE_TTL).toBeDefined();
      expect(typeof CACHE_TTL).toBe('object');
    });

    it('should have reasonable TTL values', () => {
      Object.values(CACHE_TTL).forEach(ttl => {
        expect(typeof ttl).toBe('number');
        expect(ttl).toBeGreaterThan(0);
      });
    });
  });

  describe('Cache Cleanup', () => {
    it('should clean up expired entries', async () => {
      const key1 = 'key1';
      const key2 = 'key2';
      const shortTTL = 50;
      const longTTL = 1000;

      cache.set(key1, 'value1', shortTTL);
      cache.set(key2, 'value2', longTTL);

      expect(cache.size()).toBe(2);

      // Wait for first key to expire
      await new Promise(resolve => setTimeout(resolve, 100));

      // Trigger cleanup by accessing cache
      cache.get(key1);
      cache.get(key2);

      // Only the non-expired key should remain
      expect(cache.has(key1)).toBe(false);
      expect(cache.has(key2)).toBe(true);
    });
  });
});
