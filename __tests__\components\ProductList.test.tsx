import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import ProductList from '../../components/ProductList';
import { AuthProvider } from '../../context/AuthContext';
import { CartProvider } from '../../context/CartContext';
import { WishlistProvider } from '../../context/WishlistContext';
import { ComparisonProvider } from '../../context/ComparisonContext';
import type { Product } from '../../types';

// Mock products data
const mockProducts: Product[] = [
  {
    id: 'test-1',
    name: 'Test Shirt',
    price: 29.99,
    originalPrice: 39.99,
    image: '/test-image.jpg',
    category: 'Shirts',
    brand: 'TestBrand',
    rating: 4.5,
    reviewCount: 10,
    description: 'A test shirt',
    sizes: ['S', 'M', 'L'],
    colors: ['Blue', 'Red'],
    tags: ['casual', 'cotton'],
    offer: { type: 'percentage', value: 25 },
    storeId: 'store-1'
  },
  {
    id: 'test-2',
    name: 'Test Pants',
    price: 49.99,
    originalPrice: 49.99,
    image: '/test-pants.jpg',
    category: 'Pants',
    brand: 'TestBrand',
    rating: 4.0,
    reviewCount: 5,
    description: 'Test pants',
    sizes: ['30', '32', '34'],
    colors: ['Black', 'Navy'],
    tags: ['formal', 'cotton'],
    storeId: 'store-1'
  }
];

// Test wrapper with all providers
const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <AuthProvider>
    <CartProvider>
      <WishlistProvider>
        <ComparisonProvider>
          {children}
        </ComparisonProvider>
      </WishlistProvider>
    </CartProvider>
  </AuthProvider>
);

describe('ProductList Component', () => {
  const mockOnProductSelect = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders products correctly', () => {
    render(
      <TestWrapper>
        <ProductList 
          products={mockProducts} 
          onProductSelect={mockOnProductSelect}
          searchQuery=""
        />
      </TestWrapper>
    );

    expect(screen.getByText('Test Shirt')).toBeInTheDocument();
    expect(screen.getByText('Test Pants')).toBeInTheDocument();
    expect(screen.getByText('$29.99')).toBeInTheDocument();
    expect(screen.getByText('$49.99')).toBeInTheDocument();
  });

  it('displays offer badges correctly', () => {
    render(
      <TestWrapper>
        <ProductList 
          products={mockProducts} 
          onProductSelect={mockOnProductSelect}
          searchQuery=""
        />
      </TestWrapper>
    );

    expect(screen.getByText('25% OFF')).toBeInTheDocument();
  });

  it('handles product selection', () => {
    render(
      <TestWrapper>
        <ProductList 
          products={mockProducts} 
          onProductSelect={mockOnProductSelect}
          searchQuery=""
        />
      </TestWrapper>
    );

    const productCard = screen.getByText('Test Shirt').closest('div');
    fireEvent.click(productCard!);

    expect(mockOnProductSelect).toHaveBeenCalledWith('test-1');
  });

  it('shows empty state when no products', () => {
    render(
      <TestWrapper>
        <ProductList 
          products={[]} 
          onProductSelect={mockOnProductSelect}
          searchQuery=""
        />
      </TestWrapper>
    );

    expect(screen.getByText('No products found')).toBeInTheDocument();
  });

  it('shows search results message when searching', () => {
    render(
      <TestWrapper>
        <ProductList 
          products={mockProducts} 
          onProductSelect={mockOnProductSelect}
          searchQuery="shirt"
        />
      </TestWrapper>
    );

    expect(screen.getByText(/Found \d+ results for "shirt"/)).toBeInTheDocument();
  });

  it('handles wishlist actions', async () => {
    render(
      <TestWrapper>
        <ProductList 
          products={mockProducts} 
          onProductSelect={mockOnProductSelect}
          searchQuery=""
        />
      </TestWrapper>
    );

    const wishlistButtons = screen.getAllByLabelText(/Add to wishlist/);
    fireEvent.click(wishlistButtons[0]);

    // Wait for the wishlist action to complete
    await waitFor(() => {
      expect(screen.getByLabelText(/Remove from wishlist/)).toBeInTheDocument();
    });
  });

  it('handles comparison actions', async () => {
    render(
      <TestWrapper>
        <ProductList 
          products={mockProducts} 
          onProductSelect={mockOnProductSelect}
          searchQuery=""
        />
      </TestWrapper>
    );

    const compareButtons = screen.getAllByLabelText(/Add to comparison/);
    fireEvent.click(compareButtons[0]);

    await waitFor(() => {
      expect(screen.getByLabelText(/Remove from comparison/)).toBeInTheDocument();
    });
  });

  it('displays ratings correctly', () => {
    render(
      <TestWrapper>
        <ProductList 
          products={mockProducts} 
          onProductSelect={mockOnProductSelect}
          searchQuery=""
        />
      </TestWrapper>
    );

    expect(screen.getByText('4.5')).toBeInTheDocument();
    expect(screen.getByText('(10)')).toBeInTheDocument();
    expect(screen.getByText('4.0')).toBeInTheDocument();
    expect(screen.getByText('(5)')).toBeInTheDocument();
  });

  it('shows personalized feed when no search query', () => {
    render(
      <TestWrapper>
        <ProductList 
          products={mockProducts} 
          onProductSelect={mockOnProductSelect}
          searchQuery=""
        />
      </TestWrapper>
    );

    // PersonalizedFeed should be rendered when no search query
    expect(screen.getByText(/Recommended for You/)).toBeInTheDocument();
  });

  it('handles loading state', () => {
    render(
      <TestWrapper>
        <ProductList 
          products={[]} 
          onProductSelect={mockOnProductSelect}
          searchQuery=""
          isLoading={true}
        />
      </TestWrapper>
    );

    expect(screen.getByText('Loading products...')).toBeInTheDocument();
  });

  it('applies correct CSS classes for responsive design', () => {
    render(
      <TestWrapper>
        <ProductList 
          products={mockProducts} 
          onProductSelect={mockOnProductSelect}
          searchQuery=""
        />
      </TestWrapper>
    );

    const productGrid = screen.getByRole('main').querySelector('.grid');
    expect(productGrid).toHaveClass('grid-cols-1', 'sm:grid-cols-2', 'lg:grid-cols-3', 'xl:grid-cols-4');
  });

  it('handles keyboard navigation', () => {
    render(
      <TestWrapper>
        <ProductList 
          products={mockProducts} 
          onProductSelect={mockOnProductSelect}
          searchQuery=""
        />
      </TestWrapper>
    );

    const firstProduct = screen.getByText('Test Shirt').closest('div');
    firstProduct?.focus();
    
    fireEvent.keyDown(firstProduct!, { key: 'Enter' });
    expect(mockOnProductSelect).toHaveBeenCalledWith('test-1');
  });
});
