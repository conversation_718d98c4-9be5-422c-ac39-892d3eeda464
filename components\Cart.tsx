
import { Fragment, useMemo, type FC } from 'react';
import { useCart } from '../context/CartContext';
import { products as allProducts } from '../data';
import Icon from './Icon';

interface CartProps {
    isOpen: boolean;
    onClose: () => void;
    onCheckoutClick: () => void;
}

const Cart: FC<CartProps> = ({ isOpen, onClose, onCheckoutClick }) => {
    const { cartItems, updateQuantity, removeFromCart, clearCart } = useCart();

    const cartDetails = useMemo(() => {
        return cartItems.map(item => {
            const product = allProducts.find(p => p.id === item.productId);
            if (!product) return null;
            const variant = product.variants.find(v => v.id === item.variantId);
            if (!variant) return null;
            return {
                ...item,
                name: product.name,
                image: variant.imageUrl,
                color: variant.color,
                size: variant.size,
            };
        }).filter(Boolean);
    }, [cartItems]);

    const subtotal = useMemo(() => {
        return cartDetails.reduce((total, item) => {
            if (!item) return total;
            return total + item.unitPrice * item.quantity;
        }, 0);
    }, [cartDetails]);

    if (!isOpen) return null;

    return (
        <div className="relative z-50" aria-labelledby="slide-over-title" role="dialog" aria-modal="true">
            <div className={`fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity ${isOpen ? 'ease-in-out duration-500 opacity-100' : 'ease-in-out duration-500 opacity-0'}`}></div>

            <div className="fixed inset-0 overflow-hidden">
                <div className="absolute inset-0 overflow-hidden">
                    <div className={`pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10 transform transition ease-in-out duration-500 sm:duration-700 ${isOpen ? 'translate-x-0' : 'translate-x-full'}`}>
                        <div className="pointer-events-auto w-screen max-w-md">
                            <div className="flex h-full flex-col overflow-y-scroll bg-white shadow-xl">
                                <div className="flex-1 overflow-y-auto py-6 px-4 sm:px-6">
                                    <div className="flex items-start justify-between">
                                        <h2 className="text-lg font-medium text-gray-900" id="slide-over-title">Shopping cart</h2>
                                        <div className="ml-3 flex h-7 items-center">
                                            <button type="button" className="-m-2 p-2 text-gray-400 hover:text-gray-500" onClick={onClose}>
                                                <span className="sr-only">Close panel</span>
                                                <Icon name="close" className="h-6 w-6"/>
                                            </button>
                                        </div>
                                    </div>

                                    <div className="mt-8">
                                        <div className="flow-root">
                                            {cartDetails.length > 0 ? (
                                            <ul role="list" className="-my-6 divide-y divide-gray-200">
                                                {cartDetails.map((item) => item && (
                                                    <li key={item.variantId} className="flex py-6">
                                                        <div className="h-24 w-24 flex-shrink-0 overflow-hidden rounded-md border border-gray-200">
                                                            <img src={item.image} alt={item.name} className="h-full w-full object-cover object-center" />
                                                        </div>

                                                        <div className="ml-4 flex flex-1 flex-col">
                                                            <div>
                                                                <div className="flex justify-between text-base font-medium text-gray-900">
                                                                    <h3>{item.name}</h3>
                                                                    <p className="ml-4">${(item.unitPrice * item.quantity).toFixed(2)}</p>
                                                                </div>
                                                                <p className="mt-1 text-sm text-gray-500">{item.color}</p>
                                                                <p className="mt-1 text-sm text-gray-500">Size: {item.size}</p>
                                                            </div>
                                                            <div className="flex flex-1 items-end justify-between text-sm">
                                                                <div className="flex items-center border border-gray-300 rounded-md">
                                                                    <button onClick={() => updateQuantity(item.variantId, item.quantity - 1)} className="p-1"><Icon name="minus" className="w-4 h-4"/></button>
                                                                    <p className="w-8 text-center text-gray-700">Qty {item.quantity}</p>
                                                                    <button onClick={() => updateQuantity(item.variantId, item.quantity + 1)} className="p-1"><Icon name="plus" className="w-4 h-4"/></button>
                                                                </div>
                                                                <div className="flex">
                                                                    <button type="button" onClick={() => removeFromCart(item.variantId)} className="font-medium text-indigo-600 hover:text-indigo-500 flex items-center gap-1">
                                                                        <Icon name="trash" className="w-4 h-4"/> Remove
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                ))}
                                            </ul>
                                            ) : (
                                                <div className="text-center py-20">
                                                    <Icon name="cart" className="w-16 h-16 mx-auto text-gray-300"/>
                                                    <h3 className="mt-2 text-lg font-medium text-gray-900">Your cart is empty</h3>
                                                    <p className="mt-1 text-sm text-gray-500">Start adding some products!</p>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                </div>
                                {cartDetails.length > 0 && (
                                <div className="border-t border-gray-200 py-6 px-4 sm:px-6">
                                    <div className="flex justify-between text-base font-medium text-gray-900">
                                        <p>Subtotal</p>
                                        <p>${subtotal.toFixed(2)}</p>
                                    </div>
                                    <p className="mt-0.5 text-sm text-gray-500">Shipping and taxes calculated at checkout.</p>
                                    <div className="mt-6">
                                        <button onClick={onCheckoutClick} className="flex w-full items-center justify-center rounded-md border border-transparent bg-indigo-600 px-6 py-3 text-base font-medium text-white shadow-sm hover:bg-indigo-700">Checkout</button>
                                    </div>
                                    <div className="mt-6 flex justify-center text-center text-sm text-gray-500">
                                        <p>
                                            or&nbsp;
                                            <button type="button" className="font-medium text-indigo-600 hover:text-indigo-500" onClick={onClose}>
                                                Continue Shopping
                                                <span aria-hidden="true"> &rarr;</span>
                                            </button>
                                        </p>
                                    </div>
                                </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Cart;
