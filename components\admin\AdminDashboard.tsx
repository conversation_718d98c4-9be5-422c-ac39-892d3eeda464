
import { useState, type FC } from 'react';
import Icon from '../Icon';
import ProductManagement from './ProductManagement';
import OrderManagement from './OrderManagement';
import UserManagement from './UserManagement';

type AdminView = 'products' | 'orders' | 'users' | 'analytics';

const NavLink: FC<{ view: AdminView, label: string, iconName: any, activeView: AdminView, onClick: () => void }> = 
({ view, label, iconName, activeView, onClick }) => (
    <button
        onClick={onClick}
        className={`flex w-full items-center gap-3 px-4 py-2.5 rounded-lg text-sm font-medium transition-colors ${
            activeView === view
                ? 'bg-indigo-100 text-indigo-700'
                : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
        }`}
    >
        <Icon name={iconName} className="w-5 h-5" />
        {label}
    </button>
);


const AdminDashboard: FC = () => {
    const [activeView, setActiveView] = useState<AdminView>('products');

    const renderActiveView = () => {
        switch (activeView) {
            case 'products':
                return <ProductManagement />;
            case 'orders':
                return <OrderManagement />;
            case 'users':
                return <UserManagement />;
            case 'analytics':
                return <div className="text-center p-10"><h2 className="text-xl font-semibold">Analytics Dashboard (Coming Soon)</h2></div>;
            default:
                return null;
        }
    };
    
    return (
        <div className="flex flex-col md:flex-row min-h-[calc(100vh-10rem)] bg-white rounded-xl shadow-lg overflow-hidden">
            {/* Sidebar */}
            <nav className="w-full md:w-64 flex-shrink-0 border-b md:border-b-0 md:border-r border-gray-200 p-4">
                <h2 className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-4 px-4 hidden md:block">Admin Menu</h2>
                <div className="flex flex-row md:flex-col justify-around md:justify-start md:space-y-2">
                    <NavLink view="products" label="Products" iconName="tag" activeView={activeView} onClick={() => setActiveView('products')} />
                    <NavLink view="orders" label="Orders" iconName="archive-box" activeView={activeView} onClick={() => setActiveView('orders')} />
                    <NavLink view="users" label="Users" iconName="users" activeView={activeView} onClick={() => setActiveView('users')} />
                    <NavLink view="analytics" label="Analytics" iconName="chart-bar" activeView={activeView} onClick={() => setActiveView('analytics')} />
                </div>
            </nav>

            {/* Main Content */}
            <main className="flex-1 p-4 sm:p-6 lg:p-8 overflow-y-auto">
                {renderActiveView()}
            </main>
        </div>
    );
};

export default AdminDashboard;
