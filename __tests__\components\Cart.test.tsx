import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import Cart from '../../components/Cart';
import { AuthProvider } from '../../context/AuthContext';
import { CartProvider, useCart } from '../../context/CartContext';
import type { CartItem } from '../../types';

// Mock cart items
const mockCartItems: CartItem[] = [
  {
    id: 'cart-1',
    productId: 'prod-1',
    name: 'Test Shirt',
    price: 29.99,
    image: '/test-image.jpg',
    size: 'M',
    color: 'Blue',
    quantity: 2
  },
  {
    id: 'cart-2',
    productId: 'prod-2',
    name: 'Test Pants',
    price: 49.99,
    image: '/test-pants.jpg',
    size: '32',
    color: 'Black',
    quantity: 1
  }
];

// Mock cart context
const MockCartProvider = ({ children, items = mockCartItems }: { children: React.ReactNode; items?: CartItem[] }) => {
  const mockCartContext = {
    items,
    addItem: vi.fn(),
    removeItem: vi.fn(),
    updateQuantity: vi.fn(),
    clearCart: vi.fn(),
    getItemCount: () => items.reduce((sum, item) => sum + item.quantity, 0),
    getTotal: () => items.reduce((sum, item) => sum + (item.price * item.quantity), 0)
  };

  return (
    <CartProvider value={mockCartContext}>
      {children}
    </CartProvider>
  );
};

const TestWrapper = ({ children, cartItems = mockCartItems }: { children: React.ReactNode; cartItems?: CartItem[] }) => (
  <AuthProvider>
    <MockCartProvider items={cartItems}>
      {children}
    </MockCartProvider>
  </AuthProvider>
);

describe('Cart Component', () => {
  const mockOnClose = vi.fn();
  const mockOnCheckout = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders cart items correctly', () => {
    render(
      <TestWrapper>
        <Cart isOpen={true} onClose={mockOnClose} onCheckout={mockOnCheckout} />
      </TestWrapper>
    );

    expect(screen.getByText('Test Shirt')).toBeInTheDocument();
    expect(screen.getByText('Test Pants')).toBeInTheDocument();
    expect(screen.getByText('$29.99')).toBeInTheDocument();
    expect(screen.getByText('$49.99')).toBeInTheDocument();
  });

  it('displays correct quantities', () => {
    render(
      <TestWrapper>
        <Cart isOpen={true} onClose={mockOnClose} onCheckout={mockOnCheckout} />
      </TestWrapper>
    );

    const quantityInputs = screen.getAllByRole('spinbutton');
    expect(quantityInputs[0]).toHaveValue(2);
    expect(quantityInputs[1]).toHaveValue(1);
  });

  it('calculates total correctly', () => {
    render(
      <TestWrapper>
        <Cart isOpen={true} onClose={mockOnClose} onCheckout={mockOnCheckout} />
      </TestWrapper>
    );

    // Total should be (29.99 * 2) + (49.99 * 1) = 109.97
    expect(screen.getByText('$109.97')).toBeInTheDocument();
  });

  it('handles quantity updates', () => {
    const mockUpdateQuantity = vi.fn();
    
    render(
      <TestWrapper>
        <Cart isOpen={true} onClose={mockOnClose} onCheckout={mockOnCheckout} />
      </TestWrapper>
    );

    const increaseButtons = screen.getAllByLabelText(/Increase quantity/);
    fireEvent.click(increaseButtons[0]);

    // Should call updateQuantity with correct parameters
    expect(mockUpdateQuantity).toHaveBeenCalledWith('cart-1', 3);
  });

  it('handles item removal', () => {
    const mockRemoveItem = vi.fn();
    
    render(
      <TestWrapper>
        <Cart isOpen={true} onClose={mockOnClose} onCheckout={mockOnCheckout} />
      </TestWrapper>
    );

    const removeButtons = screen.getAllByLabelText(/Remove item/);
    fireEvent.click(removeButtons[0]);

    expect(mockRemoveItem).toHaveBeenCalledWith('cart-1');
  });

  it('shows empty cart message when no items', () => {
    render(
      <TestWrapper cartItems={[]}>
        <Cart isOpen={true} onClose={mockOnClose} onCheckout={mockOnCheckout} />
      </TestWrapper>
    );

    expect(screen.getByText('Your cart is empty')).toBeInTheDocument();
    expect(screen.getByText('Start shopping to add items to your cart')).toBeInTheDocument();
  });

  it('handles checkout button click', () => {
    render(
      <TestWrapper>
        <Cart isOpen={true} onClose={mockOnClose} onCheckout={mockOnCheckout} />
      </TestWrapper>
    );

    const checkoutButton = screen.getByText('Proceed to Checkout');
    fireEvent.click(checkoutButton);

    expect(mockOnCheckout).toHaveBeenCalled();
  });

  it('handles close button click', () => {
    render(
      <TestWrapper>
        <Cart isOpen={true} onClose={mockOnClose} onCheckout={mockOnCheckout} />
      </TestWrapper>
    );

    const closeButton = screen.getByLabelText(/Close cart/);
    fireEvent.click(closeButton);

    expect(mockOnClose).toHaveBeenCalled();
  });

  it('displays item details correctly', () => {
    render(
      <TestWrapper>
        <Cart isOpen={true} onClose={mockOnClose} onCheckout={mockOnCheckout} />
      </TestWrapper>
    );

    expect(screen.getByText('Size: M')).toBeInTheDocument();
    expect(screen.getByText('Color: Blue')).toBeInTheDocument();
    expect(screen.getByText('Size: 32')).toBeInTheDocument();
    expect(screen.getByText('Color: Black')).toBeInTheDocument();
  });

  it('handles keyboard navigation', () => {
    render(
      <TestWrapper>
        <Cart isOpen={true} onClose={mockOnClose} onCheckout={mockOnCheckout} />
      </TestWrapper>
    );

    const checkoutButton = screen.getByText('Proceed to Checkout');
    checkoutButton.focus();
    
    fireEvent.keyDown(checkoutButton, { key: 'Enter' });
    expect(mockOnCheckout).toHaveBeenCalled();
  });

  it('shows loading state during operations', async () => {
    render(
      <TestWrapper>
        <Cart isOpen={true} onClose={mockOnClose} onCheckout={mockOnCheckout} />
      </TestWrapper>
    );

    const removeButton = screen.getAllByLabelText(/Remove item/)[0];
    fireEvent.click(removeButton);

    // Should show loading state
    await waitFor(() => {
      expect(screen.getByText('Removing...')).toBeInTheDocument();
    });
  });

  it('handles cart clear functionality', () => {
    const mockClearCart = vi.fn();
    
    render(
      <TestWrapper>
        <Cart isOpen={true} onClose={mockOnClose} onCheckout={mockOnCheckout} />
      </TestWrapper>
    );

    const clearButton = screen.getByText('Clear Cart');
    fireEvent.click(clearButton);

    expect(mockClearCart).toHaveBeenCalled();
  });

  it('displays correct item count in header', () => {
    render(
      <TestWrapper>
        <Cart isOpen={true} onClose={mockOnClose} onCheckout={mockOnCheckout} />
      </TestWrapper>
    );

    // Total items should be 2 + 1 = 3
    expect(screen.getByText('Shopping Cart (3)')).toBeInTheDocument();
  });

  it('handles modal overlay click', () => {
    render(
      <TestWrapper>
        <Cart isOpen={true} onClose={mockOnClose} onCheckout={mockOnCheckout} />
      </TestWrapper>
    );

    const overlay = screen.getByTestId('cart-overlay');
    fireEvent.click(overlay);

    expect(mockOnClose).toHaveBeenCalled();
  });

  it('prevents modal content click from closing', () => {
    render(
      <TestWrapper>
        <Cart isOpen={true} onClose={mockOnClose} onCheckout={mockOnCheckout} />
      </TestWrapper>
    );

    const modalContent = screen.getByTestId('cart-content');
    fireEvent.click(modalContent);

    expect(mockOnClose).not.toHaveBeenCalled();
  });
});
